# Environment Variables
.env
.env.local
.env.*.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
venv.bak/
env.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Temporary files
temp/
*.tmp
*.temp

# Test coverage
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
processed/
batch-results/
test-results/
results-v2/
original-pdf-examples/
*.pdf
*.jpg
*.jpeg
*.png
*.tiff

# Secrets and API keys
secrets/
keys/
certificates/

# Database
*.db
*.sqlite
*.sqlite3

# Node modules (if any)
node_modules/