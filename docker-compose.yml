services:
  chromoforge:
    build:
      context: .
      dockerfile: Dockerfile
      tags:
        - "chromoforge:${CHROMOFORGE_VERSION:-1.0.0}"
        - "chromoforge:latest"
    image: "chromoforge:${CHROMOFORGE_VERSION:-1.0.0}"
    container_name: chromoforge-app
    environment:
      # Hot reload settings
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
      # Load environment variables from .env file
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      - RETRY_DELAY=${RETRY_DELAY:-1.0}
      - BATCH_SIZE=${BATCH_SIZE:-5}
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10}
      - MAX_FILE_SIZE_MB=${MAX_FILE_SIZE_MB:-50}
      - SUPPORTED_FORMATS=${SUPPORTED_FORMATS:-pdf}
      - OUTPUT_DIR=${OUTPUT_DIR:-./processed}
      - TEMP_DIR=${TEMP_DIR:-./temp}
      - CONFIDENCE_THRESHOLD=${CONFIDENCE_THRESHOLD:-0.7}
      - ENABLE_COORDINATE_TRACKING=${ENABLE_COORDINATE_TRACKING:-true}
      - OBFUSCATION_METHOD=${OBFUSCATION_METHOD:-black_box}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      - GEMINI_MODEL=${GEMINI_MODEL:-gemini-2.5-pro}
      - GEMINI_TEMPERATURE=${GEMINI_TEMPERATURE:-0.1}
      - GEMINI_MAX_TOKENS=${GEMINI_MAX_TOKENS:-8192}
      - GEMINI_THINKING_BUDGET=${GEMINI_THINKING_BUDGET:--1}
      - ENABLE_ULTRA_THINK=${ENABLE_ULTRA_THINK:-true}
      - ENABLE_GOOGLE_SEARCH=${ENABLE_GOOGLE_SEARCH:-true}
      - ENABLE_URL_CONTEXT=${ENABLE_URL_CONTEXT:-true}
      - ENABLE_DATABASE_RECORDING=${ENABLE_DATABASE_RECORDING:-true}
      - DEFAULT_ORGANIZATION_ID=${DEFAULT_ORGANIZATION_ID:-}
      - THAI_CROSS_REFERENCE=${THAI_CROSS_REFERENCE:-true}
      - CONTEXTUAL_NAME_MAPPING=${CONTEXTUAL_NAME_MAPPING:-true}
      - MEDICAL_FIELD_EXTRACTION=${MEDICAL_FIELD_EXTRACTION:-true}
    volumes:
      # Mount source code for development (hot reload)
      - ./src:/app/src:ro
      # Mount input and output directories
      - ./original-pdf-examples:/app/original-pdf-examples:ro
      - ./test-results:/app/test-results
      - ./batch-results:/app/batch-results
      - ./processed:/app/processed
      - ./temp:/app/temp
      # Mount environment file
      - ./.env:/app/.env:ro
    working_dir: /app
    stdin_open: true
    tty: true
    restart: unless-stopped
    # Override default command for interactive use
    command: tail -f /dev/null

  # Optional: Add a service for running specific commands
  chromoforge-runner:
    extends: chromoforge
    container_name: chromoforge-runner
    profiles: ["runner"]
    command: python -m src.main --help
